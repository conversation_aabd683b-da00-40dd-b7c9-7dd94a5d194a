My SaaS Backend API
[![Tests](https://img.shields.io/badge/tests-passing-green)](tests/) [![Deploy](https://img.shields.io/badge/deploy-ready-blue)](docker-compose.prod.yml)

## Introduction
This is the main API repository for My SaaS Backend. It is built using Python FastAPI, MySQL, Redis and OpenAI. This application is fully dockerized. It uses docker-compose to manage the containers.

## Setting up the project

### Step 1
Install your preferred IDE. We recommend using PyCharm or VS Code.

### Step 2
Install Docker Desktop

### Step 3
Setup ngrok (optional for local development)

### Step 4
Create the docker volumes

```bash
docker volume create saas-mysql
docker volume create saas-redis
docker volume create saas-logs
docker volume create saas-backups
```

### Step 5
Create the docker network

```bash
docker network create saas-network
```

### Step 6
Clone the repository via terminal & cd into the directory

```bash
git clone <your-repository-url>
cd My_SaaS_Backend
```

### Step 7
Build & start the containers

```bash
docker compose up --build -d
```

### Step 8
Setup testing

```bash
docker exec -it saas-backend-app /bin/bash
> python setup_mysql_all_envs.py --env test
> python run_tests.py --type all
```

## Useful commands:
- **Building docker containers** - `docker compose up --build -d`
- **View all containers** - `docker ps -a`
- **Stop all containers** - `docker stop $(docker ps -aq)`
- **Prune all stopped containers** - `docker system prune -a`
- **Login onto API container** - `docker exec -it saas-backend-app /bin/bash`
- **Running tests locally** - `python run_tests.py --type unit --verbose`
- **Database migrations** - `alembic upgrade head`
- **Generate migration** - `alembic revision --autogenerate -m "description"`

## Useful docs
- [Production Deployment Guide](PRODUCTION_DEPLOYMENT_GUIDE.md) - Complete production setup guide
- [Security Guide](SECURITY_GUIDE.md) - Security implementation and best practices
- [Testing Guide](TESTING_GUIDE.md) - Testing framework and guidelines
- [API Documentation](http://localhost:8000/docs) - Interactive API documentation

## ⚠️ Important

**For database schema changes:**
1. **Removing a column:**
   - Remove the column's usage from the code, and deploy
   - Remove the column from the database schema, and deploy

2. **Adding a column:**
   - Add the column to the database schema, and deploy
   - Add the column's usage to the code, and deploy

**For environment variables:**
- Never commit `.env` files with real secrets
- Use `.env.production` template for production setup
- Generate secure JWT secrets: `python -c "import secrets; print(secrets.token_urlsafe(64))"`

**For logging:**
- Add `[keep-forever]` to critical log entries
- Logs without `[keep-forever]` will be deleted after 30 days

## Health monitoring access commands

**Note:** Please make sure you have configured proper access credentials before executing these commands.

### Application health dashboard
```bash
# Check application health
curl http://localhost:8000/health

# Check database health
curl http://localhost:8000/database/mysql/health

# View application metrics
curl http://localhost:8000/metrics
```

### Database access commands
```bash
# Access MySQL container
docker exec -it saas-backend-mysql mysql -u root -p

# Database backup
curl -X POST -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8000/database/mysql/backup
```

## Instructions to download database backup
```bash
# Download backup from container
docker cp saas-backend-app:/app/backups/backup_YYYYMMDD.sql ./local_backup.sql

# Or use API endpoint
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:8000/database/mysql/backup/download/backup_YYYYMMDD.sql
```

## Instructions to analyze Redis data

### Step 1
Install redis-cli locally
```bash
# macOS
brew install redis

# Ubuntu/Debian
sudo apt-get install redis-tools
```

### Step 2
If you have a Redis dump file locally:
```bash
redis-server --dbfilename dump.rdb --dir /path/to/directory/with/dump.rdb
```

Or to connect to remote Redis server:

**Step 2.1** Port-forwarding Redis
```bash
docker port saas-backend-redis
# Then connect using the exposed port
```

**Step 2.2** Access Redis CLI
```bash
redis-cli -h localhost -p 6379
```

### Step 3
Install Redis Insight software to analyze the data:
https://redis.io/docs/latest/operate/redisinsight/install/

### Step 4
Open Redis Insight and connect to the Redis server using the connection details from Step 2.

### Step 5
You can analyze the data in Redis Insight. Click on the analysis tab to see detailed analysis of data in the Redis server.